'use strict'
const axios = require('axios')

/**
 * 平台适配器基类
 * 定义所有平台适配器必须实现的接口
 */
class BaseAdapter {
  constructor(config) {
    console.log('BaseAdapter constructor', config)
    this.config = config
    this.platformType = config.platform_type
    this.platformName = config.platform_name
    this.token = config.token
    this.cookie = config.cookie
    this.headers = config.headers || {}
    this.autoLogin = config.auto_login || false
    this.username = config.username
    this.password = config.password

    // {{ AURA-X: Modify - 优化请求处理机制，移除强制间隔限制，增加统一延迟返回. Approval: 寸止(ID:1735372900). }}
    this.requestQueue = []
    this.isProcessingQueue = false
    this.maxRetries = 3 // 最大重试次数
    this.retryDelay = 2000 // 重试延迟2秒
    this.responseDelay = 1000 // API响应后统一延迟1秒返回
  }

  /**
   * 登录平台获取访问令牌
   * 子类必须实现此方法
   * @returns {Promise<Object>} 登录结果 { success: boolean, token: string, message: string }
   */
  async login() {
    throw new Error('子类必须实现 login 方法')
  }

  /**
   * 检查登录状态
   * {{ AURA-X: Modify - 简化登录状态检查，统一使用token存在性判断. Approval: 寸止(ID:1735372900). }}
   * @returns {Promise<boolean>} 是否已登录
   */
  async checkLoginStatus() {
    // 简单检查token是否存在，实际登录状态检测在executeRequest中统一处理
    return this.token && this.token.length > 0
  }

  /**
   * 获取货架列表
   * 子类必须实现此方法
   * @returns {Promise<Array>} 货架列表
   */
  async getShelfList() {
    throw new Error('子类必须实现 getShelfList 方法')
  }

  /**
   * 上架货架
   * 子类必须实现此方法
   * @param {string} shelfId 货架ID
   * @returns {Promise<Object>} 操作结果 { success: boolean, message: string }
   */
  async onShelf(shelfId) {
    throw new Error('子类必须实现 onShelf 方法')
  }

  /**
   * 下架货架
   * 子类必须实现此方法
   * @param {string} shelfId 货架ID
   * @returns {Promise<Object>} 操作结果 { success: boolean, message: string }
   */
  async offShelf(shelfId) {
    throw new Error('子类必须实现 offShelf 方法')
  }

  /**
   * 发送HTTP请求的通用方法 (支持重试机制和统一延迟返回)
   * 注意：请求会立即发送到第三方API，但响应会在5秒后返回给调用方
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise<Object>} 响应数据 (API响应后延迟5秒返回)
   */
  async request(url, options = {}) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ url, options, resolve, reject, retries: 0 })
      this.processQueue()
    })
  }

  /**
   * 处理请求队列 (优化版本：移除强制间隔，支持并发处理)
   */
  async processQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return
    }

    this.isProcessingQueue = true

    // {{ AURA-X: Modify - 移除强制间隔限制，允许并发请求处理. Approval: 寸止(ID:1735372900). }}
    while (this.requestQueue.length > 0) {
      const requestItem = this.requestQueue.shift()

      // 异步处理每个请求，不阻塞队列
      this.processRequest(requestItem)
    }

    this.isProcessingQueue = false
  }

  /**
   * 处理单个请求 (支持重试和统一延迟返回)
   * @param {Object} requestItem 请求项
   */
  async processRequest(requestItem) {
    try {
      // {{ AURA-X: Add - 执行请求并实现统一延迟返回机制. Approval: 寸止(ID:1735372900). }}
      const response = await this.executeRequest(requestItem.url, requestItem.options)

      // API响应成功后，延迟5秒再返回给调用方
      setTimeout(() => {
        console.log(`延迟返回响应: ${requestItem.url}`)
        requestItem.resolve(response)
      }, this.responseDelay)

    } catch (error) {
      // 检查是否需要重试
      if (requestItem.retries < this.maxRetries && this.shouldRetry(error)) {
        requestItem.retries++
        console.log(`请求失败，准备第${requestItem.retries}次重试: ${requestItem.url}`)

        // 延迟后重新处理请求
        setTimeout(() => {
          this.processRequest(requestItem)
        }, this.retryDelay * requestItem.retries)
      } else {
        // 错误情况下立即返回，不延迟
        requestItem.reject(error)
      }
    }
  }

  /**
   * 执行实际的HTTP请求 (立即发送到第三方API)
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise<Object>} 响应数据 (注意：调用方会在5秒后收到此响应)
   */
  async executeRequest(url, options = {}) {
    try {
      // {{ AURA-X: Modify - 重构executeRequest方法，提高代码可读性和可维护性. Approval: 寸止(ID:1735380100). }}

      // 构建并发送初始请求
      const response = await this.executeHttpRequest(url, options)

      // 检查登录状态并处理登录过期
      if (this.isLoginExpired(response.data)) {
        return await this.handleLoginExpired(url, options)
      }

      // 检查是否需要刷新token
      await this.checkAndRefreshToken(response.data)

      return response.data
    } catch (error) {
      return await this.handleRequestError(error, url, options)
    }
  }

  /**
   * 构建axios请求配置
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Object} axios配置对象
   */
  buildRequestConfig(url, options = {}) {
    // {{ AURA-X: Add - 提取请求配置构建逻辑，减少代码重复. Approval: 寸止(ID:1735380100). }}
    const defaultHeaders = {
      ...this.headers
    }

    // 添加Cookie认证
    if (this.cookie) {
      defaultHeaders['Cookie'] = this.cookie
    }

    // 更新认证头信息
    this.updateAuthHeaders(options.headers || {})

    return {
      method: options.method || 'GET',
      url: url,
      headers: {
        ...defaultHeaders,
        ...options.headers
      },
      timeout: options.timeout || 30000,
      data: options.data,
      validateStatus: function (status) {
        return status >= 200 && status < 300
      }
    }
  }

  /**
   * 更新认证头信息
   * @param {Object} headers 请求头对象
   */
  updateAuthHeaders(headers) {
    // {{ AURA-X: Add - 统一认证头更新逻辑，支持多种平台认证方式. Approval: 寸止(ID:1735380100). }}
    if (!this.token) return

    // 支持的认证头类型映射
    const authHeaderTypes = ['AccessToken', 'Token', 'Authorization']

    authHeaderTypes.forEach(headerType => {
      if (headers.hasOwnProperty(headerType)) {
        headers[headerType] = this.token
      }
    })
  }

  /**
   * 执行HTTP请求
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise<Object>} axios响应对象
   */
  async executeHttpRequest(url, options = {}) {
    // {{ AURA-X: Add - 提取HTTP请求执行逻辑，统一请求处理. Approval: 寸止(ID:1735380100). }}
    const axiosConfig = this.buildRequestConfig(url, options)

    console.log(`发送请求: ${axiosConfig.method} ${url}`)

    const response = await axios(axiosConfig)

    console.log(`请求成功: ${url}`)

    return response
  }

  /**
   * 处理登录过期情况
   * @param {string} url 原始请求URL
   * @param {Object} options 原始请求选项
   * @returns {Promise<Object>} 重试请求的响应数据
   */
  async handleLoginExpired(url, options) {
    // {{ AURA-X: Add - 提取登录过期处理逻辑，提高代码可读性. Approval: 寸止(ID:1735380100). }}
    console.log(`检测到${this.platformType}登录已过期`)

    if (!this.autoLogin || !this.username || !this.password) {
      throw new Error(`${this.platformType}登录已过期，请重新登录`)
    }

    // 执行自动重新登录
    const loginResult = await this.performAutoLogin()

    // 使用新token重试原始请求
    return await this.retryRequestWithNewToken(url, options, loginResult.token)
  }

  /**
   * 执行自动重新登录
   * @returns {Promise<Object>} 登录结果
   */
  async performAutoLogin() {
    // {{ AURA-X: Add - 提取自动登录逻辑，增强错误处理. Approval: 寸止(ID:1735380100). }}
    console.log(`尝试自动重新登录${this.platformType}`)

    const loginResult = await this.login()

    if (!loginResult.success) {
      throw new Error(`${this.platformType}自动重新登录失败: ${loginResult.message}`)
    }

    this.token = loginResult.token
    console.log(`${this.platformType}自动重新登录成功`)

    return loginResult
  }

  /**
   * 使用新token重试请求
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @param {string} newToken 新的token（用于验证token一致性）
   * @returns {Promise<Object>} 重试请求的响应数据
   */
  async retryRequestWithNewToken(url, options, newToken) {
    // {{ AURA-X: Add - 提取重试请求逻辑，增强错误处理和日志记录. Approval: 寸止(ID:1735380100). }}
    console.log(`使用新token重新执行请求: ${url}`)

    // 验证token一致性
    if (this.token !== newToken) {
      console.warn(`Token不一致，使用实例token: ${this.token}`)
    }

    try {
      // 更新请求选项中的认证头
      const retryOptions = { ...options }
      if (!retryOptions.headers) {
        retryOptions.headers = {}
      }

      // 确保使用最新的token
      this.updateAuthHeaders(retryOptions.headers)

      // 执行重试请求
      const retryResponse = await this.executeHttpRequest(url, retryOptions)

      console.log(`重新请求成功: ${url}`)

      // 检查重试请求是否需要刷新token
      await this.checkAndRefreshToken(retryResponse.data)

      return retryResponse.data
    } catch (retryError) {
      console.error(`重试请求失败: ${url}`, retryError.message)
      throw new Error(`重试请求失败: ${retryError.message}`)
    }
  }

  /**
   * 处理请求错误
   * @param {Error} error 错误对象
   * @param {string} url 请求URL
   * @param {Object} options 请求选项（保留用于未来扩展）
   * @returns {Promise<never>} 抛出处理后的错误
   */
  async handleRequestError(error, url, options) {
    // {{ AURA-X: Add - 提取错误处理逻辑，统一错误处理和日志记录. Approval: 寸止(ID:1735380100). }}
    console.error(`请求失败: ${url}`, error.message)

    // 检查是否是认证失败，需要重新登录
    if (this.isAuthError(error)) {
      console.log('检测到认证失败，尝试自动重新登录')
      await this.handleAuthError()
    }

    // 记录请求选项用于调试（避免未使用参数警告）
    if (options && Object.keys(options).length > 0) {
      console.debug(`请求选项:`, JSON.stringify(options, null, 2))
    }

    // 根据错误类型返回具体的错误信息
    if (error.response) {
      throw new Error(`HTTP请求失败: ${error.response.status} ${error.response.statusText}`)
    } else if (error.request) {
      throw new Error('网络请求超时或无响应')
    } else {
      throw new Error(`请求配置错误: ${error.message}`)
    }
  }

  /**
   * 判断是否应该重试
   * @param {Error} error 错误对象
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(error) {
    // 网络错误或5xx服务器错误可以重试
    return error.message.includes('网络') ||
      error.message.includes('超时') ||
      (error.response && error.response.status >= 500)
  }

  /**
   * 检查是否是认证错误
   * @param {Error} error 错误对象
   * @returns {boolean} 是否是认证错误
   */
  isAuthError(error) {
    return error.response && (error.response.status === 401 || error.response.status === 403)
  }

  /**
   * 处理认证错误 (自动重新登录)
   */
  async handleAuthError() {
    if (this.autoLogin && this.username && this.password) {
      try {
        const loginResult = await this.login()
        if (loginResult.success) {
          console.log('自动重新登录成功')
        }
      } catch (error) {
        console.error('自动重新登录失败:', error)
      }
    }
  }

  /**
   * 检查登录是否过期
   * @param {Object} responseData 响应数据
   * @returns {boolean} 是否登录过期
   */
  isLoginExpired(responseData) {
    // 子类必须重写此方法来实现特定平台的登录状态检测
    // 基类默认实现：记录响应数据用于调试
    if (responseData) {
      console.debug(`检查登录状态，响应数据:`, JSON.stringify(responseData, null, 2))
    }
    return false
  }

  /**
   * 检查并刷新token
   * @param {Object} responseData 响应数据
   */
  async checkAndRefreshToken(responseData) {
    // 子类可以重写此方法来实现特定平台的token刷新逻辑
    // 基类默认实现：记录响应数据用于调试
    if (responseData) {
      console.debug(`检查token刷新，响应数据:`, JSON.stringify(responseData, null, 2))
    }
  }

  /**
   * 更新平台配置（通用方法）
   * @param {Object} updateData 更新数据
   * @returns {Promise<void>}
   */
  async updatePlatformConfig(updateData) {
    // {{ AURA-X: Add - 提取平台配置更新逻辑到基类，减少重复代码. Approval: 寸止(ID:1735373300). }}
    const DatabaseManager = require('../database-manager')
    const dbManager = new DatabaseManager()

    if (this.config._id) {
      // 使用配置ID精确更新
      await dbManager.updatePlatformConfigById(this.config._id, updateData)
    } else {
      // 兼容旧版本：使用原有方法
      await dbManager.updatePlatformConfig(this.config.user_id, this.platformType, updateData)
    }
  }

  /**
   * 更新登录成功状态
   * @param {string} token 新的token
   * @returns {Promise<void>}
   */
  async updateLoginSuccess(token) {
    // {{ AURA-X: Add - 提取登录成功状态更新逻辑，统一处理. Approval: 寸止(ID:1735373300). }}
    this.token = token
    await this.updatePlatformConfig({
      token: this.token,
      login_status: 1,
      last_login_time: new Date()
    })
  }

  /**
   * 更新登录失败状态
   * @returns {Promise<void>}
   */
  async updateLoginFailure() {
    // {{ AURA-X: Add - 提取登录失败状态更新逻辑，统一处理. Approval: 寸止(ID:1735373300). }}
    await this.updatePlatformConfig({
      login_status: 2
    })
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 统一状态码转换
   * 将各平台的状态码转换为统一的状态码
   * @param {any} platformStatus 平台原始状态
   * @returns {Object} 统一状态 { unified_state: number }
   */
  convertStatus(platformStatus) {
    // 子类可以重写此方法实现平台特定的状态转换
    return {
      unified_state: 0, // 0待租，1出租中，-1下架，-2其他
      platform_status: platformStatus
    }
  }

  /**
   * 从货架列表中查找单个货架状态
   * @param {string} shelfId 货架ID
   * @param {Array} shelfList 货架列表（可选，如果不提供则调用getShelfList）
   * @returns {Promise<Object>} 货架状态
   */
  async getShelfStatusFromList(shelfId, shelfList = null) {
    try {
      // 如果没有提供货架列表，则获取完整列表
      const shelves = shelfList || await this.getShelfList()
      const shelf = shelves.find(item => item.id === shelfId)

      if (!shelf) {
        throw new Error(`未找到货架 ID: ${shelfId}`)
      }

      return {
        unified_state: shelf.unified_state,
        platform_status: shelf.platform_status
      }
    } catch (error) {
      console.error(`从列表中获取货架状态失败 (${this.platformType}):`, error)
      throw error
    }
  }

  /**
   * 获取平台信息
   * @returns {Object} 平台信息
   */
  getPlatformInfo() {
    return {
      type: this.platformType,
      name: this.platformName,
      autoLogin: this.autoLogin
    }
  }
}

module.exports = BaseAdapter
