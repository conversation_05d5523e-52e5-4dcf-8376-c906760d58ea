<template>
  <responsive-layout
    :page-title="'货架管理'"
  >
    <responsive-container>
      <!-- 搜索和筛选 -->
      <view class="search-filter">
      <view class="search-box">
        <uv-input
          v-model="searchKeyword"
          placeholder="搜索游戏账号"
          border="none"
          prefixIcon="search"
          prefixIconStyle="color: #999; font-size: 32rpx;"
          :customStyle="{
            backgroundColor: '#f5f5f5',
            borderRadius: '24rpx',
            padding: '12rpx 20rpx'
          }"
          @input="onSearchInput"
        />
      </view>
      <!-- 平台筛选 -->
      <view class="platform-tabs">
        <view
          class="filter-tab"
          :class="{ active: selectedPlatform === 'all' }"
          @click="onPlatformChange('all')"
        >
          全部平台
        </view>
        <view
          v-for="platform in platformList"
          :key="platform.platform_type"
          class="filter-tab"
          :class="{ active: selectedPlatform === platform.platform_type }"
          @click="onPlatformChange(platform.platform_type)"
        >
          {{ platform.platform_name }}
        </view>
      </view>
      <!-- 状态筛选 -->
      <view class="filter-tabs">
        <view
          v-for="tab in filterTabs"
          :key="tab.value"
          class="filter-tab"
          :class="{ active: currentFilter === tab.value }"
          @click="onFilterChange(tab.value)"
        >
          {{ tab.label }}
        </view>
      </view>
    </view>
      <!-- 统计信息 -->
      <responsive-grid
        :mobile-cols="4"
        :tablet-cols="4"
        :desktop-cols="4"
        gap="sm"
        class="stats"
      >
        <view class="stat-item grid-item">
          <text class="stat-value">{{ filteredList.length }}</text>
          <text class="stat-label">当前显示</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.rented }}</text>
          <text class="stat-label">出租中</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.available }}</text>
          <text class="stat-label">可出租</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ totalStats.offline }}</text>
          <text class="stat-label">已下架</text>
        </view>
      </responsive-grid>
      <!-- 货架列表 -->
      <responsive-grid
        :mobile-cols="1"
        :tablet-cols="1"
        :desktop-cols="2"
        gap="base"
        class="shelf-list"
      >
        <view
          v-for="shelf in filteredList"
          :key="shelf._id"
          class="shelf-item grid-item clickable"
        >
        <view class="shelf-header">
          <view class="shelf-info">
            <text class="shelf-title">{{ shelf.shelf_title }}</text>
            <view class="shelf-tags">
              <text class="platform-tag">{{ getPlatformName(shelf.platform_type) }}</text>
              <text
                class="status-tag"
                :class="StateManager.getStateClass(shelf.unified_state)"
              >
                {{ StateManager.getStateText(shelf.unified_state) }}
              </text>
            </view>
          </view>
        </view>
        <view class="shelf-details">
          <view class="detail-row">
            <view class="detail-item">
              <text class="detail-label">游戏账号：</text>
              <text class="detail-value">{{ shelf.game_account }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">游戏：</text>
              <text class="detail-value">{{ shelf.game_name }}</text>
            </view>
          </view>
          <view class="detail-row">
            <view class="detail-item">
              <text class="detail-label">角色名：</text>
              <text class="detail-value">{{ shelf.game_role_name || '未设置' }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">价格：</text>
              <text class="detail-value price">¥{{ shelf.rent_price }}/小时</text>
            </view>
          </view>
          <view class="detail-row">
            <view class="detail-item">
              <text class="detail-label">最小租期：</text>
              <text class="detail-value">{{ shelf.min_rent_time }}小时</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">最后更新：</text>
              <text class="detail-value">{{ formatTime(shelf.last_sync_time) }}</text>
            </view>
          </view>
        </view>
        <view class="shelf-footer">
          <view class="monitor-switch">
            <text class="switch-label">监控状态：</text>
            <uv-switch :value="shelf.is_active" size="40" @change="(value) => toggleMonitor(shelf, value)"></uv-switch>
          </view>
          <view class="shelf-actions">
            <uv-button
              :type="shelf.unified_state === StateManager.STATES.AVAILABLE ? 'warning' : 'primary'"
              size="small"
              :loading="shelf.operating"
              @click="toggleShelfStatus(shelf)"
              :customStyle="{
                borderRadius: '100px',
                fontSize: '24rpx',
                padding: '0 24rpx'
              }"
              :text="StateManager.getActionButtonText(shelf.unified_state)"
            >
            </uv-button>
          </view>
        </view>
      </view>
      <!-- 空状态 -->
      <view v-if="filteredList.length === 0 && !loading" class="empty-state">
        <text class="empty-icon">📦</text>
        <text class="empty-text">{{ emptyStateText }}</text>
        <text class="empty-desc">{{ emptyStateDesc }}</text>
        <uv-button
          v-if="platformList.length === 0"
          type="primary"
          @click="goToConfig"
          :customStyle="{
            borderRadius: '24rpx',
            marginTop: '20rpx'
          }"
          text="前往配置"
        >
        </uv-button>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && !loading && shelfList.length > 0" class="load-more">
        <uv-button
          type="info"
          :loading="isLoadingMore"
          @click="loadMore"
          :customStyle="{
            borderRadius: '20rpx'
          }"
          :text="isLoadingMore ? '加载中...' : '加载更多'"
        >
        </uv-button>
        </view>
      </responsive-grid>

      <!-- 加载更多 -->
      <view v-if="hasMore && !loading && shelfList.length > 0" class="load-more">
        <uv-button
          type="info"
          :loading="isLoadingMore"
          @click="loadMore"
          :customStyle="{
            borderRadius: '20rpx'
          }"
          :text="isLoadingMore ? '加载中...' : '加载更多'"
          class="bun-hover"
        >
        </uv-button>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading">
        <uv-loading-icon mode="circle" size="40"></uv-loading-icon>
        <text style="margin-left: 20rpx;">加载中...</text>
      </view>
    </responsive-container>
  </responsive-layout>
</template>
<script>
import { callFunction } from '@/utils/request.js'
import { StateManager } from '@/common/js/state-manager.js'
// {{ AURA-X: Add - 引入响应式布局组件. Approval: 寸止(ID:1735374000). }}
import ResponsiveLayout from '@/components/layout/responsive-layout.vue'
import ResponsiveContainer from '@/components/layout/responsive-container.vue'
import ResponsiveGrid from '@/components/layout/responsive-grid.vue'
// {{ AURA-X: Add - 引入公共工具函数，减少重复代码. Approval: 寸止(ID:1735374000). }}
import utils from '@/common/js/utils.js'
export default {
  components: {
    ResponsiveLayout,
    ResponsiveContainer,
    ResponsiveGrid
  },
  name: 'ShelfList',
  data() {
    return {
      StateManager,
      shelfList: [],
      platformList: [],
      searchKeyword: '',
      currentFilter: 'all',
      selectedPlatform: 'all',
      loading: false,
      isLoadingMore: false,
      hasMore: true,
      // {{ AURA-X: Modify - 使用常量替代硬编码值. Approval: 寸止(ID:1735374000). }}
      currentPage: utils.getConstant('DEFAULT_CURRENT_PAGE'),
      pageSize: utils.getConstant('DEFAULT_PAGE_SIZE'),
      currentTabIndex: 1, // 货架页面是第2个tab，索引为1（账号管理页面插入后仍保持不变）
      filterTabs: [
        { label: '全部', value: 'all' },
        { label: '待租', value: 'available' },
        { label: '出租中', value: 'rented' },
        { label: '已下架', value: 'offline' }
      ]
    }
  },
  computed: {
    filteredList() {
      let list = this.shelfList

      // 搜索过滤
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.trim().toLowerCase()
        list = list.filter(shelf =>
          shelf.game_account.toLowerCase().includes(keyword) ||
          shelf.shelf_title.toLowerCase().includes(keyword) ||
          shelf.game_name.toLowerCase().includes(keyword)
        )
      }

      // 状态过滤
      if (this.currentFilter !== 'all') {
        list = StateManager.filterShelfsByState(list, this.currentFilter)
      }

      return list
    },
    totalStats() {
      // 统计当前加载的数据（已经是按平台筛选的）
      return StateManager.getStateStats(this.shelfList)
    },

    emptyStateText() {
      if (this.platformList.length === 0) {
        return '暂无平台配置'
      }
      if (this.shelfList.length === 0) {
        return '暂无货架数据'
      }
      if (this.selectedPlatform !== 'all') {
        const platform = this.platformList.find(p => p.platform_type === this.selectedPlatform)
        return `${platform.platform_name || '该平台'}暂无货架数据`
      }
      return '暂无符合条件的货架'
    },

    emptyStateDesc() {
      if (this.platformList.length === 0) {
        return '请先配置平台账号信息'
      }
      if (this.shelfList.length === 0) {
        return '请先更新平台货架数据'
      }
      if (this.selectedPlatform !== 'all') {
        return '请尝试切换到其他平台或更新该平台数据'
      }
      return '请尝试调整筛选条件'
    }
  },
  async onShow() {
    await this.loadPlatformList()
    this.loadShelfList()
  },

  onReachBottom() {
    this.loadMore()
  },

  methods: {
    async loadPlatformList() {
      try {
        // {{ AURA-X: Modify - 改为获取支持的平台类型列表，显示所有平台选项. Approval: 寸止(ID:1735372900). }}
        const result = await callFunction('shelf-management', {
          action: 'getPlatformList'
        })
        if (result.code === 0) {
          // 转换API返回的数据格式
          this.platformList = result.data.map(platform => ({
            platform_type: platform.type,
            platform_name: platform.name
          })) || []

          // 如果有平台且当前选择的是'all'，则默认选中第一个平台
          if (this.platformList.length > 0 && this.selectedPlatform === 'all') {
            this.selectedPlatform = this.platformList[0].platform_type
          }
          // 如果没有平台，确保选择'all'
          if (this.platformList.length === 0) {
            this.selectedPlatform = 'all'
          }
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('加载平台列表失败:', error)
        // 错误处理：使用默认的平台列表
        this.platformList = [
          { platform_type: 'zuhaowan', platform_name: '租号玩' },
          { platform_type: 'uhaozu', platform_name: 'U号租' }
        ]
        this.selectedPlatform = 'all'
        // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
        utils.showError('平台列表加载失败')
      }
    },

    async loadShelfList(isLoadMore = false) {
      if (isLoadMore) {
        this.isLoadingMore = true
      } else {
        this.loading = true
        this.currentPage = 1
        this.hasMore = true
      }

      try {
        const requestData = {
          pageIndex: this.currentPage,
          pageSize: this.pageSize
        }

        // 如果选择了特定平台，添加平台筛选参数
        if (this.selectedPlatform !== 'all') {
          requestData.platformType = this.selectedPlatform
        }

        const result = await callFunction('shelf-management', {
          action: 'getShelfList',
          data: requestData
        })

        if (result.code === 0) {
          const newShelves = result.data.list.map(shelf => ({
            ...shelf,
            operating: false
          }))

          if (isLoadMore) {
            this.shelfList = [...this.shelfList, ...newShelves]
          } else {
            this.shelfList = newShelves
          }

          // 判断是否还有更多数据
          this.hasMore = newShelves.length === this.pageSize
          if (this.hasMore) {
            this.currentPage++
          }
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('加载货架列表失败:', error)
        // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
        utils.showError('加载失败')
      } finally {
        this.loading = false
        this.isLoadingMore = false
      }
    },

    loadMore() {
      if (this.hasMore && !this.loading && !this.isLoadingMore) {
        this.loadShelfList(true)
      }
    },

    async toggleShelfStatus(shelf) {
      shelf.operating = true
      try {
        const targetStatus = StateManager.getTargetState(shelf.unified_state)
        const result = await callFunction('shelf-management', {
          action: 'toggleShelfStatus',
          data: {
            shelfId: shelf._id,
            targetStatus: targetStatus
          }
        })
        if (result.code === 0) {
          // {{ AURA-X: Modify - 使用公共成功提示函数. Approval: 寸止(ID:1735374000). }}
          utils.showSuccess(targetStatus === StateManager.STATES.AVAILABLE ? '上架成功' : '下架成功')
          shelf.unified_state = targetStatus
        } else {
          uni.showModal({
            title: '操作失败',
            content: result.message,
            showCancel: false
          })
        }
      } catch (error) {
        console.error('切换货架状态失败:', error)
        // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
        utils.showError('操作失败')
      } finally {
        shelf.operating = false
      }
    },
    async toggleMonitor(shelf, value) {
      try {
        const newStatus = value
        
        const result = await callFunction('shelf-management', {
          action: 'toggleMonitorStatus',
          data: {
            shelfId: shelf._id,
            isActive: newStatus
          }
        })
        
        if (result.code === 0) {
          shelf.is_active = newStatus
          // {{ AURA-X: Modify - 使用公共成功提示函数. Approval: 寸止(ID:1735374000). }}
          utils.showSuccess(result.message)
        } else {
          uni.showModal({
            title: '操作失败',
            content: result.message,
            showCancel: false
          })
        }
      } catch (error) {
        console.error('切换监控状态失败:', error)
        // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
        utils.showError('操作失败')
      }
    },
    onSearchInput() {
      // 搜索输入防抖处理
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        // 触发搜索
      }, 300)
    },
    onPlatformChange(platformType) {
      this.selectedPlatform = platformType
      this.loadShelfList() // 重新加载数据
    },

    onFilterChange(filterValue) {
      this.currentFilter = filterValue
    },
    goToConfig() {
      uni.navigateTo({
        url: '/pages/platform-config/index'
      })
    },
    getPlatformName(platformType) {
      // {{ AURA-X: Modify - 优先使用动态数据，回退到公共函数. Approval: 寸止(ID:1735374000). }}
      // 优先从已加载的平台类型列表中查找
      const platform = this.platformList.find(p => p.platform_type === platformType)
      if (platform) {
        return platform.platform_name
      }
      // 回退到公共函数
      return utils.getPlatformName(platformType)
    },

    formatTime(timestamp) {
      // {{ AURA-X: Modify - 使用公共时间格式化函数，保持特殊默认值. Approval: 寸止(ID:1735374000). }}
      if (!timestamp) return '从未更新'
      return utils.formatTime(timestamp)
    }
  }
}
</script>
<style scoped lang="scss">
.container {
  padding: $spacing-base; /* 底部留出tabBar空间 */
  background: $bg-color-page;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

.search-filter {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  margin-bottom: $spacing-base;
  box-shadow: $shadow-sm;
}

.search-box {
  margin-bottom: $spacing-base;
}

.platform-tabs {
  display: flex;
  gap: $spacing-sm;
  flex-wrap: wrap;
  margin-bottom: $spacing-base;
}

.filter-tabs {
  display: flex;
  gap: $spacing-sm;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 8rpx $spacing-sm;
  border-radius: $border-radius-pill;
  font-size: $font-size-sm;
  color: $text-color-secondary;
  background: $bg-color-overlay;
  border: 1rpx solid $border-color-light;
  transition: all $transition-fast;

  &.active {
    background: $primary-color;
    color: white;
    border-color: $primary-color;
  }

  &:active {
    opacity: 0.8;
  }
}
.stats {
  display: flex;
  gap: $spacing-sm;
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: $font-size-xxl;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: 8rpx;
  display: block;
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  font-weight: $font-weight-normal;
}
.shelf-item {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-base;
  box-shadow: $shadow-base;
}
.shelf-item:last-child {
  margin-bottom: $spacing-base;
}

.shelf-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-base;
}

.shelf-info {
  flex: 1;
}

.shelf-title {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.shelf-tags {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-wrap: wrap;
}

.platform-tag {
  font-size: $font-size-xs;
  color: $primary-color;
  background-color: $primary-color-light;
  padding: 4rpx 8rpx;
  border-radius: $border-radius-xs;
  font-weight: $font-weight-medium;
}

.status-tag {
  font-size: $font-size-xs;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;
  display: inline-block;

  &.available {
    background-color: $success-light;
    color: $success-color;
  }

  &.rented {
    background-color: $warning-light;
    color: $warning-color;
  }

  &.offline {
    background-color: $error-light;
    color: $error-color;
  }

  &.unknown {
    background-color: $info-light;
    color: $info-color;
  }
}

.shelf-actions {
  margin-left: $spacing-base;
}
.action-btn {
  height: 50rpx;
  line-height: 50rpx;
  border: none;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  margin: 0;
}

.online-btn {
  background: $success-color;
  color: white;
}

.offline-btn {
  background: $error-color;
  color: white;
}

.shelf-details {
  margin-bottom: $spacing-base;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
  margin-bottom: $spacing-sm;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-item {
  display: flex;
  align-items: center;
  min-width: 0;
  flex: 1;

  &.full-width {
    flex: 100%;
  }
}

.detail-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-right: 8rpx;
  white-space: nowrap;
}

.detail-value {
  font-size: $font-size-sm;
  color: $text-color-primary;
  word-break: break-all;

  &.price {
    color: $error-color;
    font-weight: $font-weight-bold;
  }

  &.error {
    color: $error-color;
    font-size: $font-size-xs;
  }
}
.shelf-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: $spacing-base;
  border-top: 1rpx solid $border-color-light;
}

.monitor-switch {
  display: flex;
  align-items: center;
}

.switch-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-right: $spacing-sm;
}

.empty-state {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: $spacing-lg;
  color: $text-color-placeholder;
}

.empty-text {
  font-size: $font-size-lg;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
  font-weight: $font-weight-medium;
}

.empty-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
  margin-bottom: $spacing-xl;
}

.load-more {
  text-align: center;
  padding: $spacing-base;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  color: $text-color-secondary;
}

</style>