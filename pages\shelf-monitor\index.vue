<template>
  <responsive-layout
    :page-title="'货架监控'"
    @logout="handleLogout"
  >
    <responsive-container>
      <!-- 移动端用户信息栏 (PC端在布局组件中显示) -->
      <view class="user-info mobile-only">
        <view class="user-avatar">
          <text class="avatar-text">{{ userInfo.nickname ? userInfo.nickname.charAt(0) : 'U' }}</text>
        </view>
        <view class="user-details">
          <view class="user-name">{{ userInfo.nickname || userInfo.username || '用户' }}</view>
          <view class="user-phone">{{ userInfo.mobile || '' }}</view>
        </view>
        <view class="user-actions">
          <uv-button
            type="info"
            size="small"
            @click="handleLogout"
            :customStyle="{borderRadius: '100px'}"
            text="退出"
          >
          </uv-button>
        </view>
      </view>

      <!-- 顶部状态卡片 -->
      <view class="status-section">
        <view class="section-header">
          <text class="section-title">数据概览</text>
          <text class="section-subtitle">实时统计数据</text>
        </view>
        <view class="status-cards">
          <view class="status-cards-grid">
            <view class="status-card">
              <view class="card-title">平台配置</view>
              <view class="card-value">{{ platformStats.total }}</view>
              <view class="card-desc">{{ platformStats.active }}个已激活</view>
            </view>
            <view class="status-card">
              <view class="card-title">货架总数</view>
              <view class="card-value">{{ shelfStats.total }}</view>
              <view class="card-desc">{{ shelfStats.active }}个监控中</view>
            </view>
            <view class="status-card">
              <view class="card-title">出租中</view>
              <view class="card-value">{{ shelfStats.rented }}</view>
              <view class="card-desc">{{ shelfStats.available }}个可租</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 平台状态和最近日志 -->
      <responsive-grid
        :mobile-cols="1"
        :tablet-cols="1"
        :desktop-ols="2"
        gap="base"
        class="content-grid"
      >
        <!-- 平台状态 -->
        <view class="platform-section grid-item">
          <view class="section-header">
            <text class="section-title">平台状态</text>
            <text class="section-subtitle">各平台登录状态</text>
          </view>
          <view class="platform-list">
            <view
              v-for="platform in platformList"
              :key="platform._id"
              class="platform-item clickable"
            >
              <view class="platform-info">
                <view class="platform-info-left">
                  <view class="platform-header">
                    <view class="platform-name">{{ platform.platform_name }}</view>
                  </view>
                  <view class="platform-meta">
                    <text class="meta-text">最后登录：{{ formatTime(platform.last_login_time) }}</text>
                  </view>
                </view>
                <view class="platform-status">
                  <text class="status-dot" :class="getStatusClass(platform.login_status)"></text>
                  <text class="status-text">{{ getStatusText(platform.login_status) }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 最近日志 -->
        <view class="logs-section grid-item">
      <view class="section-header">
        <view class="header-left">
          <text class="section-title">最近日志</text>
          <text class="section-subtitle">最新操作记录</text>
        </view>
            <text class="more-link clickable" @click="goToLogs">查看更多</text>
      </view>
      <view class="log-list">
        <view
          v-for="log in recentLogs"
          :key="log._id"
          class="log-item clickable"
        >
          <view class="log-content">
            <view class="log-header">
              <view class="log-title">{{ getActionText(log.action) }}</view>
              <view class="log-status" :class="log.status ? 'success' : 'error'">
                {{ log.status ? '成功' : '失败' }}
              </view>
            </view>
            <view class="log-desc">
              <text class="log-platform">{{ getPlatformName(log.platform_type) }}</text>
              <text class="log-separator">·</text>
              <text class="log-account">{{ log.game_account }}</text>
            </view>
            <view class="log-time">{{ formatTime(log.create_time) }}</view>
          </view>
        </view>
          </view>
        </view>
      </responsive-grid>
    </responsive-container>
  </responsive-layout>
</template>

<script>
import { getUserInfo, logout } from '@/utils/auth.js'
import { callFunction } from '@/utils/request.js'
// {{ AURA-X: Add - 引入响应式布局组件. Approval: 寸止(ID:**********). }}
import ResponsiveLayout from '@/components/layout/responsive-layout.vue'
import ResponsiveContainer from '@/components/layout/responsive-container.vue'
import ResponsiveGrid from '@/components/layout/responsive-grid.vue'
// {{ AURA-X: Add - 引入公共工具函数，减少重复代码. Approval: 寸止(ID:**********). }}
import utils from '@/common/js/utils.js'
export default {
  name: 'ShelfMonitor',
  components: {
    ResponsiveLayout,
    ResponsiveContainer,
    ResponsiveGrid
  },
  data() {
    return {
      userInfo: {},
      platformStats: {
        total: 0,
        active: 0
      },
      shelfStats: {
        total: 0,
        active: 0,
        rented: 0,
        available: 0
      },
      // {{ AURA-X: Add - 新增详细统计数据存储. Approval: 寸止(ID:1735373700). }}
      detailedStats: {
        inactive: 0,
        offline: 0,
        other: 0,
        percentages: {
          available: 0,
          rented: 0,
          offline: 0,
          other: 0
        }
      },
      platformList: [],
      recentLogs: [],
      currentTabIndex: 0 // 监控页面是第1个tab，索引为0
    }
  },
  onShow() {
    this.loadData()
  },
  onLoad() {
    // 获取用户信息
    this.userInfo = getUserInfo()
  },
  methods: {
    async loadData() {
      try {
        await Promise.all([
          this.loadShelfStats(),
          this.loadPlatformList(),
          this.loadRecentLogs()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:**********). }}
        utils.showError('加载数据失败')
      }
    },
    /**
     * 加载货架统计数据
     *
     * 使用专门的统计接口获取轻量级统计数据，提高加载性能
     */
    async loadShelfStats() {
      try {
        // {{ AURA-X: Modify - 使用专门的统计接口替代完整列表接口，提高性能. Approval: 寸止(ID:1735373700). }}
        const result = await callFunction('shelf-management', {
          action: 'getShelfStats'
        })

        if (result.code === 0) {
          const statsData = result.data

          // 更新统计数据
          this.shelfStats.total = statsData.total
          this.shelfStats.active = statsData.active
          this.shelfStats.rented = statsData.states.rented
          this.shelfStats.available = statsData.states.available

          // 可选：存储更详细的统计信息供其他地方使用
          this.detailedStats = {
            inactive: statsData.inactive,
            offline: statsData.states.offline,
            other: statsData.states.other,
            percentages: statsData.percentages
          }
        }
      } catch (error) {
        console.error('加载货架统计失败:', error)
        // 降级处理：如果统计接口失败，可以回退到原有方式
      }
    },

    // 获取平台列表和统计数据
    async loadPlatformList() {
      try {
        const result = await callFunction('shelf-management', {
          action: 'getPlatformConfigs'
        })
        if (result.code === 0) {
          this.platformList = result.data
          this.platformStats.total = result.data.length
          this.platformStats.active = result.data.filter(c => c.login_status === 1).length
        }
      } catch (error) {
        console.error('加载平台列表失败:', error)
      }
    },
    async loadRecentLogs() {
      try {
        const result = await callFunction('shelf-management', {
          action: 'getOperationLogs',
          data: {
            pageSize: 5
          }
        })
        if (result.code === 0) {
          this.recentLogs = result.data.list
        }
      } catch (error) {
        console.error('加载最近日志失败:', error)
      }
    },
    goToLogs() {
      uni.switchTab({
        url: '/pages/operation-logs/index'
      })
    },
    getStatusClass(status) {
      switch (status) {
        case 1: return 'success'
        case 2: return 'error'
        default: return 'warning'
      }
    },
    getStatusText(status) {
      switch (status) {
        case 1: return '已登录'
        case 2: return '登录失效'
        default: return '未登录'
      }
    },
    // {{ AURA-X: Remove - 移除重复的工具函数，使用公共utils. Approval: 寸止(ID:**********). }}
    getActionText(action) {
      return utils.getActionText(action)
    },
    getPlatformName(platformType) {
      return utils.getPlatformName(platformType)
    },
    formatTime(timestamp) {
      return utils.formatTime(timestamp)
    },

    // 退出登录
    async handleLogout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: async (res) => {
          if (res.confirm) {
            // {{ AURA-X: Modify - 使用公共加载提示函数. Approval: 寸止(ID:**********). }}
            utils.showLoading('退出中...')
            try {
              await logout()
            } catch (error) {
              console.error('退出登录失败:', error)
            } finally {
              utils.hideLoading()
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/* 响应式内容网格 */
.content-grid {
  margin-top: $spacing-base;
  margin-bottom: $spacing-base;

  @include desktop-up {
    margin-top: $spacing-xl;
  }
}

/* 用户信息栏 (仅移动端显示) */
.user-info {
  display: flex;
  align-items: center;
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  @include responsive-spacing(padding, base);
  @include responsive-spacing(margin-bottom, base);
  box-shadow: $shadow-base;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: $primary-gradient;
  display: flex;
  align-items: center;
  justify-content: center;
  @include responsive-spacing(margin-right, base);
}

.avatar-text {
  @include responsive-font(lg);
  font-weight: $font-weight-bold;
  color: white;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  margin-bottom: 4rpx;
}

.user-phone {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.user-actions {
  margin-left: $spacing-base;
}

.title-text {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  letter-spacing: 1rpx;
}

.title-desc {
  font-size: 26rpx;
  color: #8c8c8c;
}

/* 区块样式 */
.status-section,
.platform-section,
.logs-section {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-base;
}

/* 区块标题 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-base;
}

.header-left {
  flex: 1;
}

.section-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  margin-bottom: 4rpx;
  display: block;
}

.section-subtitle {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.more-link {
  font-size: $font-size-sm;
  color: $primary-color;
  font-weight: $font-weight-medium;

  &:active {
    opacity: 0.7;
  }
}

/* 状态卡片 */
.status-cards {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  @include responsive-spacing(padding, base);
  box-shadow: $shadow-base;
}

.status-cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: $spacing-sm;

  @include desktop-up {
    gap: $spacing-base;
  }
}

.status-card {
  background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
  border-radius: $border-radius-base;
  @include responsive-spacing(padding, base);
  border: 1rpx solid $border-color-light;
  position: relative;
  overflow: hidden;
  text-align: center;
  transition: all $transition-base;

  // 移动端优化
  @include mobile-only {
    padding: $spacing-sm;
  }

  @include desktop-up {
    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: $primary-gradient;
  }
}

.card-title {
  @include responsive-font(sm);
  color: $text-color-secondary;
  margin-bottom: 8rpx;
  font-weight: $font-weight-normal;
}

.card-value {
  @include responsive-font(xxl);
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: 8rpx;
  display: block;

  // 移动端字体调整
  @include mobile-only {
    font-size: $font-size-lg;
    margin-bottom: 4rpx;
  }
}

.card-desc {
  @include responsive-font(xs);
  color: $text-color-placeholder;

  // 移动端字体调整
  @include mobile-only {
    font-size: 20rpx;
  }
}

/* 平台列表 */
.platform-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;

  @include desktop-up {
    gap: $spacing-sm-pc;
  }
}

.platform-item {
  background: $bg-color-overlay;
  border-radius: $border-radius-base;
  @include responsive-spacing(padding, base);
  border: 1rpx solid $border-color-light;
  transition: all $transition-base;

  &:active {
    background: $bg-color-hover;
  }

  @include desktop-up {
    &:hover {
      background: $bg-color-hover;
      transform: translateY(-1px);
      box-shadow: $shadow-sm;
    }
  }
}

.platform-info {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.platform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.platform-name {
  @include responsive-font(base);
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.platform-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;

  &.success {
    background-color: $success-color;
    box-shadow: 0 0 8rpx rgba(90, 199, 37, 0.3);
  }

  &.error {
    background-color: $error-color;
    box-shadow: 0 0 8rpx rgba(245, 108, 108, 0.3);
  }

  &.warning {
    background-color: $warning-color;
    box-shadow: 0 0 8rpx rgba(249, 174, 61, 0.3);
  }
}

.status-text {
  @include responsive-font(sm);
  color: $text-color-secondary;
  font-weight: $font-weight-medium;
}

.platform-meta {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.meta-text {
  font-size: $font-size-xs;
  color: $text-color-placeholder;
}

/* 日志列表 */
.log-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;

  @include desktop-up {
    gap: $spacing-sm-pc;
  }
}

.log-item {
  background: $bg-color-overlay;
  border-radius: $border-radius-base;
  @include responsive-spacing(padding, base);
  border: 1rpx solid $border-color-light;
  transition: all $transition-base;

  &:active {
    background: $bg-color-hover;
  }

  @include desktop-up {
    &:hover {
      background: $bg-color-hover;
      transform: translateY(-1px);
      box-shadow: $shadow-sm;
    }
  }
}

.log-content {
  width: 100%;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.log-title {
  @include responsive-font(base);
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.log-status {
  @include responsive-font(xs);
  padding: 4rpx 12rpx;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;

  &.success {
    background-color: $success-light;
    color: $success-color;
  }

  &.error {
    background-color: $error-light;
    color: $error-color;
  }
}

.log-desc {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.log-platform {
  @include responsive-font(xs);
  color: $primary-color;
  background-color: $primary-color-light;
  padding: 4rpx 8rpx;
  border-radius: $border-radius-xs;
  font-weight: $font-weight-medium;
}

.log-separator {
  @include responsive-font(sm);
  color: $border-color-base;
  margin: 0 8rpx;
}

.log-account {
  @include responsive-font(sm);
  color: $text-color-secondary;
}

.log-time {
  @include responsive-font(xs);
  color: $text-color-placeholder;
}
</style>