'use strict'

// {{ AURA-X: Modify - 移除不再使用的直接数据库操作，统一使用DatabaseManager. Approval: 寸止(ID:**********). }}
// {{ AURA-X: Modify - 使用公共模块替代重复代码. Approval: 寸止(ID:1735372800). }}
const { PlatformAdapterFactory, Logger, AlertManager, DatabaseManager, getStateText } = require('shelf-core')

// {{ AURA-X: Modify - 添加常量定义，提高代码可读性. Approval: 寸止(ID:**********). }}
/**
 * 系统常量定义
 */
const SYSTEM_CONSTANTS = {
  LOG_RETENTION_DAYS: 1,           // 日志保留天数
  MAX_SHELVES_PER_QUERY: 100,    // 单次查询最大货架数量
  UNIFIED_STATES: {                // 统一状态码
    AVAILABLE: 0,                  // 待租
    RENTED: 1,                     // 出租中
    OFFLINE: -1                    // 下架
  }
}

/**
 * 错误处理工具类
 *
 * 提供统一的错误处理、日志记录和异常分类功能
 */
class ErrorHandler {
  constructor(logger, alertManager) {
    this.logger = logger
    this.alertManager = alertManager
  }

  /**
   * 处理系统级错误
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   * @param {Object} metadata - 附加元数据
   */
  async handleSystemError(error, context, metadata = {}) {
    // {{ AURA-X: Add - 统一系统错误处理机制. Approval: 寸止(ID:**********). }}
    const errorInfo = this._categorizeError(error)

    console.error(`[系统错误] ${context}:`, error)

    // 记录详细错误日志
    await this.logger.log({
      user_id: metadata.userId || 'system',
      platform_type: metadata.platformType || 'system',
      action: context,
      status: 0,
      message: errorInfo.message,
      error_code: errorInfo.code,
      trigger_type: metadata.triggerType || 'auto',
      ...metadata
    })

    // 严重错误需要发送告警
    if (errorInfo.severity === 'critical') {
      await this.alertManager.alertSystemError(error, context, metadata)
    }
  }

  /**
   * 处理业务级错误
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   * @param {Object} metadata - 附加元数据
   */
  async handleBusinessError(error, context, metadata = {}) {
    // {{ AURA-X: Add - 统一业务错误处理机制. Approval: 寸止(ID:**********). }}
    const errorInfo = this._categorizeError(error)

    console.error(`[业务错误] ${context}:`, error)

    // 记录业务错误日志
    await this.logger.log({
      user_id: metadata.userId || '',
      platform_type: metadata.platformType || '',
      platform_shelf_id: metadata.shelfId || '',
      game_account: metadata.gameAccount || '',
      action: context,
      status: 0,
      message: errorInfo.message,
      error_code: errorInfo.code,
      trigger_type: metadata.triggerType || 'auto'
    })
  }

  /**
   * 错误分类和严重程度判断
   * @param {Error} error - 错误对象
   * @returns {Object} 错误信息
   */
  _categorizeError(error) {
    // {{ AURA-X: Add - 智能错误分类机制. Approval: 寸止(ID:**********). }}
    const message = error.message || '未知错误'
    let code = 'UNKNOWN_ERROR'
    let severity = 'normal'

    // 网络相关错误
    if (message.includes('网络') || message.includes('timeout') || message.includes('ECONNRESET')) {
      code = 'NETWORK_ERROR'
      severity = 'normal'
    }
    // 认证相关错误
    else if (message.includes('登录') || message.includes('token') || message.includes('认证')) {
      code = 'AUTH_ERROR'
      severity = 'normal'
    }
    // 数据库相关错误
    else if (message.includes('数据库') || message.includes('database') || message.includes('collection')) {
      code = 'DATABASE_ERROR'
      severity = 'critical'
    }
    // API相关错误
    else if (message.includes('API') || message.includes('接口')) {
      code = 'API_ERROR'
      severity = 'normal'
    }
    // 配置相关错误
    else if (message.includes('配置') || message.includes('config')) {
      code = 'CONFIG_ERROR'
      severity = 'normal'
    }

    return {
      message,
      code,
      severity
    }
  }
}

/**
 * 货架监控云函数
 *
 * 主要功能：
 * 1. 定时监控各平台货架状态变化
 * 2. 检测游戏账号出租状态变化
 * 3. 自动执行联动上下架操作
 * 4. 记录详细的操作日志
 * 5. 异常情况告警通知
 *
 * 执行频率：每2分钟执行一次（由云函数触发器配置）
 *
 * @param {Object} event - 云函数事件对象
 * @param {Object} context - 云函数上下文对象
 * @returns {Object} 执行结果
 */
exports.main = async (event, context) => {
  console.log('货架监控任务开始执行', new Date())

  // 初始化核心服务组件
  const operationLogger = new Logger()
  const systemAlertManager = new AlertManager({
    serverChanKey: process.env.SERVER_CHAN_KEY || ''
  })

  // {{ AURA-X: Add - 初始化统一错误处理器. Approval: 寸止(ID:**********). }}
  const errorHandler = new ErrorHandler(operationLogger, systemAlertManager)

  const taskStartTime = Date.now()

  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    // {{ AURA-X: Modify - 修复定时任务获取平台配置的问题，使用getAllPlatformConfigs方法. Approval: 寸止(ID:1735373100). }}

    // 步骤1：获取所有启用的平台配置
    const databaseManager = new DatabaseManager()
    const activePlatformConfigs = await databaseManager.getAllPlatformConfigs(null, true)

    // 检查是否有可用的平台配置
    if (activePlatformConfigs.length === 0) {
      console.log('没有找到启用的平台配置，监控任务结束')
      return {
        code: 0,
        message: '没有找到启用的平台配置'
      }
    }

    console.log(`找到 ${activePlatformConfigs.length} 个启用的平台配置`)

    // 步骤2：按用户ID分组平台配置，便于批量处理
    const userPlatformGroups = groupPlatformConfigsByUser(activePlatformConfigs)
    console.log(`共涉及 ${Object.keys(userPlatformGroups).length} 个用户`)

    // 步骤3：逐个处理每个用户的平台配置
    for (const userId in userPlatformGroups) {
      await processUserPlatforms(userId, userPlatformGroups[userId], operationLogger, errorHandler)
    }
    // 步骤4：清理过期日志（保留指定天数）
    await cleanupExpiredLogs()

    // 计算任务执行时间并输出结果
    const totalExecutionTime = Date.now() - taskStartTime
    console.log(`货架监控任务执行完成，总耗时: ${totalExecutionTime}ms`)

    return {
      code: 0,
      message: '货架监控任务执行成功',
      executionTime: totalExecutionTime
    }
  } catch (error) {
    // {{ AURA-X: Modify - 使用统一错误处理器处理系统级错误. Approval: 寸止(ID:**********). }}
    await errorHandler.handleSystemError(error, 'monitor_task', {
      executionTime: Date.now() - taskStartTime,
      triggerType: 'auto'
    })

    return {
      code: -1,
      message: '货架监控任务执行失败',
      error: error.message
    }
  }
}

/**
 * 按用户ID分组平台配置
 *
 * 将所有平台配置按照用户ID进行分组，便于后续批量处理
 *
 * @param {Array} platformConfigs - 平台配置列表
 * @returns {Object} 按用户ID分组的平台配置对象
 */
function groupPlatformConfigsByUser(platformConfigs) {
  // {{ AURA-X: Add - 提取平台配置分组逻辑，提高代码可读性. Approval: 寸止(ID:**********). }}
  const userPlatformGroups = {}

  platformConfigs.forEach(config => {
    const userId = config.user_id

    // 如果该用户还没有分组，创建新的分组
    if (!userPlatformGroups[userId]) {
      userPlatformGroups[userId] = []
    }

    // 将配置添加到对应用户的分组中
    userPlatformGroups[userId].push(config)
  })

  return userPlatformGroups
}

/**
 * 处理单个用户的多个平台配置
 *
 * 为指定用户处理其所有平台的货架监控任务
 *
 * @param {string} userId - 用户ID
 * @param {Array} platforms - 该用户的平台配置列表
 * @param {Logger} logger - 操作日志记录器
 * @param {ErrorHandler} errorHandler - 统一错误处理器
 */
async function processUserPlatforms(userId, platforms, logger, errorHandler) {
  try {
    console.log(`开始处理用户 ${userId} 的 ${platforms.length} 个平台配置`)

    // {{ AURA-X: Modify - 拆分长函数，提高代码可读性和可维护性. Approval: 寸止(ID:**********). }}

    // 步骤1：获取用户的活跃货架数据
    const userShelfData = await getUserShelfData(userId)
    if (!userShelfData) {
      console.log(`用户 ${userId} 没有活跃的货架，跳过处理`)
      return
    }

    // 步骤2：为每个平台创建对应的适配器实例
    const platformAdapters = await createPlatformAdapters(platforms, userId, logger)
    if (Object.keys(platformAdapters).length === 0) {
      console.log(`用户 ${userId} 没有可用的平台适配器，跳过处理`)
      return
    }

    // 步骤3：批量获取所有平台的货架列表（避免重复API调用）
    const platformShelfLists = await fetchAllPlatformShelfLists(platformAdapters, userId, logger)

    // 步骤4：按游戏账号分组处理货架状态
    await processShelfsByAccount(userId, userShelfData.accountGroups, platformAdapters, platformShelfLists, logger)

    console.log(`用户 ${userId} 的所有平台处理完成`)

  } catch (error) {
    // {{ AURA-X: Modify - 使用统一错误处理器处理用户级错误. Approval: 寸止(ID:**********). }}
    await errorHandler.handleBusinessError(error, 'process_user_platforms', {
      userId: userId,
      platformType: 'system',
      triggerType: 'auto'
    })
  }
}
/**
 * 处理单个游戏账号在多个平台的货架状态
 *
 * 核心功能：
 * 1. 同步更新该账号在各平台的货架状态
 * 2. 检测账号出租状态变化
 * 3. 执行联动上下架操作
 *
 * @param {string} userId - 用户ID
 * @param {string} gameAccount - 游戏账号名称
 * @param {Array} shelves - 该账号的货架列表
 * @param {Object} platformAdapters - 平台适配器映射对象
 * @param {Object} platformShelfLists - 平台货架列表映射对象
 * @param {Logger} logger - 操作日志记录器
 */
async function processAccountShelves(userId, gameAccount, shelves, platformAdapters, platformShelfLists, logger) {
  try {
    console.log(`开始处理游戏账号 ${gameAccount} 的 ${shelves.length} 个货架`)

    // {{ AURA-X: Modify - 简化货架状态更新逻辑，提高代码可读性. Approval: 寸止(ID:**********). }}

    // 步骤1：批量更新该账号在各平台的货架状态
    const currentPlatformStatuses = await updateAllShelfStatuses(userId, gameAccount, shelves, platformAdapters, platformShelfLists, logger)

    // 步骤2：根据出租状态变化执行联动上下架操作
    await handleRentStatusChange(userId, gameAccount, shelves, currentPlatformStatuses, platformAdapters, logger)

    console.log(`游戏账号 ${gameAccount} 处理完成`)

  } catch (error) {
    console.error(`处理游戏账号 ${gameAccount} 货架失败:`, error)

    // 记录账号级别的错误日志
    await logger.log({
      user_id: userId,
      platform_type: 'system',
      game_account: gameAccount,
      action: 'process_account_shelves',
      status: 0,
      message: error.message,
      trigger_type: 'auto'
    })
  }
}
/**
 * 更新所有货架状态
 * @param {string} userId 用户ID
 * @param {string} gameAccount 游戏账号
 * @param {Array} shelves 货架列表
 * @param {Object} platformAdapters 平台适配器映射
 * @param {Object} platformShelfLists 平台货架列表映射
 * @param {Logger} logger 日志记录器
 * @returns {Object} 当前状态映射
 */
async function updateAllShelfStatuses(userId, gameAccount, shelves, platformAdapters, platformShelfLists, logger) {
  // {{ AURA-X: Add - 提取货架状态更新逻辑，统一错误处理. Approval: 寸止(ID:**********). }}
  const currentStatuses = {}

  for (const shelf of shelves) {
    const adapter = platformAdapters[shelf.platform_type]
    const shelfList = platformShelfLists[shelf.platform_type]

    if (!adapter || !shelfList) {
      console.log(`平台 ${shelf.platform_type} 适配器或货架列表不可用`)
      continue
    }

    try {
      // 从已获取的列表中查找货架状态（无需额外API调用）
      const currentStatus = await adapter.getShelfStatusFromList(shelf.platform_shelf_id, shelfList)
      currentStatuses[shelf.platform_type] = currentStatus

      // 更新本地货架状态
      await updateShelfStatus(shelf._id, currentStatus)

      await logger.log({
        user_id: userId,
        platform_type: shelf.platform_type,
        platform_shelf_id: shelf.platform_shelf_id,
        game_account: gameAccount,
        action: 'sync',
        status: 1,
        message: `更新成功，状态: ${getStateText(currentStatus.unified_state)}`,
        trigger_type: 'auto'
      })
    } catch (error) {
      console.error(`更新货架状态失败 ${shelf.platform_type}:`, error)
      await logger.log({
        user_id: userId,
        platform_type: shelf.platform_type,
        platform_shelf_id: shelf.platform_shelf_id,
        game_account: gameAccount,
        action: 'sync',
        status: 0,
        message: error.message,
        trigger_type: 'auto'
      })
    }
  }

  return currentStatuses
}

/**
 * 处理租赁状态变化，执行联动上下架
 * @param {string} userId 用户ID
 * @param {string} gameAccount 游戏账号
 * @param {Array} shelves 货架列表
 * @param {Object} currentStatuses 当前状态
 * @param {Object} platformAdapters 平台适配器
 * @param {Logger} logger 日志记录器
 */
async function handleRentStatusChange(userId, gameAccount, shelves, currentStatuses, platformAdapters, logger) {
  try {
    // {{ AURA-X: Modify - 简化租赁状态变化处理逻辑，提高代码可读性. Approval: 寸止(ID:**********). }}
    // 检查是否有平台账号正在出租
    const rentedPlatform = findRentedPlatform(currentStatuses)

    if (rentedPlatform) {
      console.log(`账号 ${gameAccount} 在平台 ${rentedPlatform} 出租中，执行其他平台下架`)
      await handleOffShelfOtherPlatforms(userId, gameAccount, shelves, rentedPlatform, currentStatuses, platformAdapters, logger)
    } else {
      console.log(`账号 ${gameAccount} 没有出租，检查是否需要重新上架`)
      await handleReOnShelf(userId, gameAccount, shelves, currentStatuses, platformAdapters, logger)
    }
  } catch (error) {
    console.error('处理租赁状态变化失败:', error)
  }
}
/**
 * 更新货架状态
 * @param {string} shelfId 货架ID
 * @param {Object} statusData 状态数据
 */
async function updateShelfStatus(shelfId, statusData) {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    const dbManager = new DatabaseManager()
    await dbManager.updateShelfStatus(shelfId, statusData)
  } catch (error) {
    console.error('更新货架状态失败:', error)
  }
}
/**
 * 获取用户货架数据
 * @param {string} userId 用户ID
 * @returns {Object|null} 货架数据对象或null
 */
async function getUserShelfData(userId) {
  // {{ AURA-X: Add - 提取用户货架数据获取逻辑，提高代码可读性. Approval: 寸止(ID:**********). }}
  const dbManager = new DatabaseManager()
  const shelfResult = await dbManager.getShelfList(userId, {
    activeOnly: true,
    pageSize: SYSTEM_CONSTANTS.MAX_SHELVES_PER_QUERY
  })

  if (shelfResult.list.length === 0) {
    return null
  }

  console.log(`用户 ${userId} 共有 ${shelfResult.list.length} 个活跃货架`)

  // 按游戏账号分组货架
  const accountGroups = {}
  shelfResult.list.forEach(shelf => {
    if (!accountGroups[shelf.game_account]) {
      accountGroups[shelf.game_account] = []
    }
    accountGroups[shelf.game_account].push(shelf)
  })

  console.log(`用户 ${userId} 的货架按账号分组完成，共 ${Object.keys(accountGroups).length} 个账号`)
  return { accountGroups, totalShelves: shelfResult.list.length }
}

/**
 * 创建平台适配器映射
 * @param {Array} platforms 平台配置列表
 * @param {string} userId 用户ID
 * @param {Logger} logger 日志记录器
 * @returns {Object} 平台适配器映射
 */
async function createPlatformAdapters(platforms, userId, logger) {
  // {{ AURA-X: Add - 提取平台适配器创建逻辑，统一错误处理. Approval: 寸止(ID:**********). }}
  const platformAdapters = {}

  for (const platformConfig of platforms) {
    try {
      const adapter = PlatformAdapterFactory.create(platformConfig.platform_type, platformConfig)
      platformAdapters[platformConfig.platform_type] = adapter
    } catch (error) {
      console.error(`创建平台适配器失败 ${platformConfig.platform_type}:`, error)
      await logger.log({
        user_id: userId,
        platform_type: platformConfig.platform_type,
        action: 'create_adapter',
        status: 0,
        message: error.message,
        trigger_type: 'auto'
      })
    }
  }

  return platformAdapters
}

/**
 * 批量获取所有平台的货架列表
 * @param {Object} platformAdapters 平台适配器映射
 * @param {string} userId 用户ID
 * @param {Logger} logger 日志记录器
 * @returns {Object} 平台货架列表映射
 */
async function fetchAllPlatformShelfLists(platformAdapters, userId, logger) {
  // {{ AURA-X: Add - 提取平台货架列表获取逻辑，统一错误处理. Approval: 寸止(ID:**********). }}
  const platformShelfLists = {}

  for (const [platformType, adapter] of Object.entries(platformAdapters)) {
    try {
      console.log(`获取${platformType}平台的完整货架列表`)
      platformShelfLists[platformType] = await adapter.getShelfList()
    } catch (error) {
      console.error(`获取${platformType}平台货架列表失败:`, error)
      await logger.log({
        user_id: userId,
        platform_type: platformType,
        action: 'get_shelf_list',
        status: 0,
        message: error.message,
        trigger_type: 'auto'
      })
    }
  }

  return platformShelfLists
}

/**
 * 按账号处理货架
 * @param {string} userId 用户ID
 * @param {Object} accountGroups 按账号分组的货架
 * @param {Object} platformAdapters 平台适配器映射
 * @param {Object} platformShelfLists 平台货架列表映射
 * @param {Logger} logger 日志记录器
 */
async function processShelfsByAccount(userId, accountGroups, platformAdapters, platformShelfLists, logger) {
  // {{ AURA-X: Add - 提取按账号处理货架逻辑，提高代码可读性. Approval: 寸止(ID:**********). }}
  for (const gameAccount in accountGroups) {
    await processAccountShelves(userId, gameAccount, accountGroups[gameAccount], platformAdapters, platformShelfLists, logger)
  }
}

/**
 * 查找正在出租的平台
 * @param {Object} currentStatuses 当前状态映射
 * @returns {string|null} 出租平台类型或null
 */
function findRentedPlatform(currentStatuses) {
  // {{ AURA-X: Add - 提取出租平台查找逻辑，提高代码可读性. Approval: 寸止(ID:**********). }}
  for (const [platformType, status] of Object.entries(currentStatuses)) {
    if (status && status.unified_state === SYSTEM_CONSTANTS.UNIFIED_STATES.RENTED) {
      return platformType
    }
  }
  return null
}

/**
 * 处理其他平台下架操作
 * @param {string} userId 用户ID
 * @param {string} gameAccount 游戏账号
 * @param {Array} shelves 货架列表
 * @param {string} rentedPlatform 出租平台
 * @param {Object} currentStatuses 当前状态
 * @param {Object} platformAdapters 平台适配器
 * @param {Logger} logger 日志记录器
 */
async function handleOffShelfOtherPlatforms(userId, gameAccount, shelves, rentedPlatform, currentStatuses, platformAdapters, logger) {
  // {{ AURA-X: Add - 提取其他平台下架逻辑，统一错误处理. Approval: 寸止(ID:**********). }}
  for (const shelf of shelves) {
    if (shelf.platform_type === rentedPlatform) {
      continue // 跳过出租平台
    }

    const adapter = platformAdapters[shelf.platform_type]
    if (!adapter) continue

    try {
      const currentStatus = currentStatuses[shelf.platform_type]
      if (currentStatus && currentStatus.unified_state === SYSTEM_CONSTANTS.UNIFIED_STATES.AVAILABLE) { // 当前是待租状态，需要下架
        await adapter.offShelf(shelf.platform_shelf_id)

        // {{ AURA-X: Add - 联动下架成功后立即更新本地数据库状态，确保数据同步. Approval: 寸止(ID:**********). }}
        // 立即更新本地数据库状态为已下架
        await updateShelfStatus(shelf._id, {
          unified_state: SYSTEM_CONSTANTS.UNIFIED_STATES.OFFLINE,
          platform_status: {}
        })

        await logger.log({
          user_id: userId,
          platform_type: shelf.platform_type,
          platform_shelf_id: shelf.platform_shelf_id,
          game_account: gameAccount,
          action: 'off_shelf',
          status: 1,
          message: `联动下架成功，因为在${rentedPlatform}平台出租中`,
          trigger_type: 'auto'
        })
      }
    } catch (error) {
      console.error(`联动下架失败 ${shelf.platform_type}:`, error)
      await logger.log({
        user_id: userId,
        platform_type: shelf.platform_type,
        platform_shelf_id: shelf.platform_shelf_id,
        game_account: gameAccount,
        action: 'off_shelf',
        status: 0,
        message: error.message,
        trigger_type: 'auto'
      })
    }
  }
}

/**
 * 处理重新上架操作
 * @param {string} userId 用户ID
 * @param {string} gameAccount 游戏账号
 * @param {Array} shelves 货架列表
 * @param {Object} currentStatuses 当前状态
 * @param {Object} platformAdapters 平台适配器
 * @param {Logger} logger 日志记录器
 */
async function handleReOnShelf(userId, gameAccount, shelves, currentStatuses, platformAdapters, logger) {
  // {{ AURA-X: Add - 提取重新上架逻辑，统一错误处理. Approval: 寸止(ID:**********). }}
  for (const shelf of shelves) {
    const adapter = platformAdapters[shelf.platform_type]
    if (!adapter) continue

    try {
      const currentStatus = currentStatuses[shelf.platform_type]
      if (currentStatus && currentStatus.unified_state === SYSTEM_CONSTANTS.UNIFIED_STATES.OFFLINE) { // 当前是下架状态，需要上架
        await adapter.onShelf(shelf.platform_shelf_id)

        // {{ AURA-X: Add - 重新上架成功后立即更新本地数据库状态，确保数据同步. Approval: 寸止(ID:**********). }}
        // 立即更新本地数据库状态为待租
        await updateShelfStatus(shelf._id, {
          unified_state: SYSTEM_CONSTANTS.UNIFIED_STATES.AVAILABLE,
          platform_status: {}
        })

        await logger.log({
          user_id: userId,
          platform_type: shelf.platform_type,
          platform_shelf_id: shelf.platform_shelf_id,
          game_account: gameAccount,
          action: 'on_shelf',
          status: 1,
          message: '自动重新上架成功',
          trigger_type: 'auto'
        })
      }
    } catch (error) {
      console.error(`自动上架失败 ${shelf.platform_type}:`, error)
      await logger.log({
        user_id: userId,
        platform_type: shelf.platform_type,
        platform_shelf_id: shelf.platform_shelf_id,
        game_account: gameAccount,
        action: 'on_shelf',
        status: 0,
        message: error.message,
        trigger_type: 'auto'
      })
    }
  }
}

/**
 * 清理过期日志（保留指定天数）
 *
 * 根据系统配置清理超过保留期限的操作日志
 */
async function cleanupExpiredLogs() {
  try {
    // {{ AURA-X: Modify - 使用DatabaseManager简化数据库操作. Approval: 寸止(ID:**********). }}
    // {{ AURA-X: Modify - 使用常量定义的日志保留天数. Approval: 寸止(ID:**********). }}
    const databaseManager = new DatabaseManager()
    await databaseManager.cleanupExpiredLogs(SYSTEM_CONSTANTS.LOG_RETENTION_DAYS)

    console.log(`过期日志清理完成，保留 ${SYSTEM_CONSTANTS.LOG_RETENTION_DAYS} 天`)
  } catch (error) {
    console.error('清理过期日志失败:', error)
  }
}