<template>
  <view class="responsive-layout">
    <!-- PC端侧边栏导航 -->
    <view class="sidebar" v-if="isDesktop">
      <view class="sidebar-header">
        <view class="logo">
          <text class="logo-text">租号雷达</text>
        </view>
      </view>
      <view class="sidebar-nav">
        <view
          v-for="(item, index) in navItems"
          :key="index"
          class="nav-item focusable"
          :class="{ active: currentPath === item.path }"
          :tabindex="0"
          @click="handleNavClick(item)"
          @keydown.enter="handleNavClick(item)"
          @keydown.space.prevent="handleNavClick(item)"
        >
          <uv-icon
            :name="item.icon"
            :color="currentPath === item.path ? '#3c9cff' : '#606266'"
            size="20"
          ></uv-icon>
          <text class="nav-text">{{ item.text }}</text>
        </view>
      </view>
    </view>

    <!-- 主内容区域 -->
    <view class="main-content" :class="{ 'with-sidebar': isDesktop }">
      <!-- PC端顶部导航栏 -->
      <view class="top-header" v-if="isDesktop">
        <view class="header-left">
          <text class="page-title">{{ pageTitle }}</text>
        </view>
        <view class="header-right">
          <view class="user-info">
            <view class="user-avatar">
              <text class="avatar-text">{{ userInfo.nickname ? userInfo.nickname.charAt(0) : 'U' }}</text>
            </view>
            <text class="user-name">{{ userInfo.nickname || userInfo.username || '用户' }}</text>
            <uv-button 
              type="info" 
              size="small" 
              @click="handleLogout"
              text="退出"
            ></uv-button>
          </view>
        </view>
      </view>

      <!-- 页面内容容器 -->
      <view class="content-container">
        <slot></slot>
      </view>
    </view>

    <!-- 移动端底部导航 -->
    <simple-tabbar 
      v-if="!isDesktop"
      :current="currentTabIndex"
      @change="handleTabChange"
    ></simple-tabbar>
  </view>
</template>

<script>
/**
 * 响应式布局组件
 * 根据设备类型自动切换PC端侧边栏布局和移动端底部导航布局
 */
export default {
  name: 'ResponsiveLayout',
  props: {
    // 当前页面标题
    pageTitle: {
      type: String,
      default: '租号货架监控'
    }
  },
  data() {
    return {
      // 导航项配置
      // {{ AURA-X: Modify - 添加账号管理页面到导航配置. Approval: 寸止(ID:**********). }}
      navItems: [
        { text: '监控', icon: 'eye', path: 'pages/shelf-monitor/index' },
        { text: '货架', icon: 'grid', path: 'pages/shelf-list/index' },
        { text: '账号', icon: 'account', path: 'pages/account-management/index' },
        { text: '配置', icon: 'setting', path: 'pages/platform-config/index' },
        { text: '日志', icon: 'file-text', path: 'pages/operation-logs/index' }
      ],
      // 当前路径
      currentPath: '',
      // 用户信息
      userInfo: {}
    }
  },
  computed: {
    // 判断是否为桌面端
    isDesktop() {
      // #ifdef H5
      return window.innerWidth >= 1024
      // #endif
      // #ifndef H5
      return false
      // #endif
    },
    // 当前tab索引
    currentTabIndex() {
      return this.navItems.findIndex(item => this.currentPath.includes(item.path))
    }
  },
  mounted() {
    this.getCurrentPath()
    this.loadUserInfo()
    
    // #ifdef H5
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
    // #endif
  },
  beforeDestroy() {
    // #ifdef H5
    window.removeEventListener('resize', this.handleResize)
    // #endif
  },
  methods: {
    // 获取当前页面路径
    getCurrentPath() {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        this.currentPath = pages[pages.length - 1].route
      }
    },
    
    // 加载用户信息
    loadUserInfo() {
      this.userInfo = this.$cache.get('userInfo') || {}
    },
    
    // 处理导航点击
    handleNavClick(item) {
      if (this.currentPath === item.path) return
      
      uni.switchTab({
        url: `/${item.path}`,
        fail: () => {
          uni.navigateTo({ url: `/${item.path}` })
        }
      })
    },
    
    // 处理底部导航切换
    handleTabChange(event) {
      this.handleNavClick(this.navItems[event.index])
    },
    
    // 处理窗口大小变化
    handleResize() {
      this.$forceUpdate()
    },
    
    // 处理退出登录
    handleLogout() {
      this.$emit('logout')
    }
  }
}
</script>

<style lang="scss" scoped>
.responsive-layout {
  display: flex;
  height: 100%;
  background: $bg-color-page;
}

/* PC端侧边栏 */
.sidebar {
  width: 240px;
  background: $bg-color-container;
  border-right: 1px solid $border-color-light;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
}

.sidebar-header {
  padding: $spacing-lg;
  border-bottom: 1px solid $border-color-light;
}

.logo-text {
  @include responsive-font(xl);
  font-weight: $font-weight-bold;
  color: $primary-color;
}

.sidebar-nav {
  flex: 1;
  padding: $spacing-base 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: $spacing-base $spacing-lg;
  cursor: pointer;
  transition: all $transition-base;
  
  &:hover {
    background: $bg-color-hover;
  }
  
  &.active {
    background: $primary-color-light;
    border-right: 3px solid $primary-color;
    
    .nav-text {
      color: $primary-color;
      font-weight: $font-weight-semibold;
    }
  }
}

.nav-text {
  @include responsive-font(base);
  color: $text-color-regular;
  margin-left: $spacing-sm;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  &.with-sidebar {
    margin-left: 240px;
  }
}

/* PC端顶部导航栏 */
.top-header {
  height: 64px;
  background: $bg-color-container;
  border-bottom: 1px solid $border-color-light;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $spacing-lg;
}

.page-title {
  @include responsive-font(lg);
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: $primary-gradient;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  @include responsive-font(sm);
  color: white;
  font-weight: $font-weight-semibold;
}

.user-name {
  @include responsive-font(base);
  color: $text-color-primary;
  font-weight: $font-weight-medium;
}

/* 内容容器 */
.content-container {
  flex: 1;
  overflow-y: auto;
  
  @include mobile-only {
    padding-bottom: 88rpx; // 为移动端底部导航留出空间
  }
}

/* 移动端适配 */
@include mobile-only {
  .responsive-layout {
    flex-direction: column;
  }
  
  .main-content {
    flex: 1;
    margin-left: 0;
  }
}
</style>
