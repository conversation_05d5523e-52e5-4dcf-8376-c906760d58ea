<template>
  <responsive-layout
    :page-title="'平台配置'"
  >
    <responsive-container>
      <!-- 顶部操作栏 -->
      <view class="header">
        <view class="header-title"></view>
        <uv-button
          type="primary"
          size="small"
          @click="goToAdd"
          icon="plus"
          iconSize="20"
          iconColor="#fff"
          :customStyle="{
            borderRadius: '100px',
            padding: '0 20rpx'
          }"
          text="添加平台"
          class="bun-hover"
        >
        </uv-button>
      </view>
      <!-- 统计信息 -->
      <view class="stats">
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value">{{ platformList.length }}</text>
            <text class="stat-label">总配置</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ activeCount }}</text>
            <text class="stat-label">已激活</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{ loginCount }}</text>
            <text class="stat-label">已登录</text>
          </view>
        </view>
      </view>
      <!-- 平台列表 -->
      <responsive-grid
        :mobile-cols="1"
        :tablet-cols="1"
        :desktop-cols="2"
        gap="base"
        class="platform-list"
      >
        <view
          v-for="platform in platformList"
          :key="platform._id"
          class="platform-item grid-item clickable"
        >
        <view class="platform-info">
          <view class="platform-header">
            <text class="platform-name">{{ platform.platform_name }}</text>
            <view class="platform-status">
              <text
                class="status-tag"
                :class="getStatusClass(platform.login_status)"
              >
                {{ getStatusText(platform.login_status) }}
              </text>
            </view>
          </view>
          <view class="platform-details">
            <view class="detail-item">
              <text class="detail-label">用户名：</text>
              <text class="detail-value">{{ platform.username || '未配置' }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">登录方式：</text>
              <text class="detail-value">{{ platform.auto_login ? '自动登录' : '手动配置' }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">最后登录：</text>
              <text class="detail-value">{{ formatTime(platform.last_login_time) }}</text>
            </view>
            <view class="detail-item" v-if="platform.remark">
              <text class="detail-label">备注：</text>
              <text class="detail-value">{{ platform.remark }}</text>
            </view>
          </view>
        </view>
        <view class="platform-actions">
          <uv-button
            type="info"
            size="small"
            @click="editPlatform(platform)"
            :customStyle="{
              borderRadius: '100px'
            }"
            text="编辑"
          >
          </uv-button>
          <uv-button
            type="primary"
            size="small"
            :loading="platform.testing"
            @click="testLogin(platform)"
            :customStyle="{
              borderRadius: '100px'
            }"
            :text="platform.testing ? '登录中' : '登录'"
          >
          </uv-button>
          <uv-button
            type="success"
            size="small"
            :loading="platform.syncing"
            @click="syncPlatform(platform)"
            :customStyle="{
              borderRadius: '100px'
            }"
            :text="platform.syncing ? '更新中' : '更新货架'"
          >
          </uv-button>
          <uv-button
            type="error"
            size="small"
            @click="deletePlatform(platform)"
            :customStyle="{
              borderRadius: '100px'
            }"
            text="删除"
          >
          </uv-button>
        </view>
        </view>
      </responsive-grid>

      <!-- 空状态 -->
      <view v-if="platformList.length === 0" class="empty-state">
        <text class="empty-icon">📋</text>
        <text class="empty-text">暂无平台配置</text>
        <text class="empty-desc">点击右上角添加平台开始使用</text>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading">
        <uv-loading-icon mode="circle" size="40"></uv-loading-icon>
        <text style="margin-left: 20rpx;">加载中...</text>
      </view>
    </responsive-container>
  </responsive-layout>
</template>
<script>
import { callFunction } from '@/utils/request.js'
// {{ AURA-X: Add - 引入响应式布局组件. Approval: 寸止(ID:1735374000). }}
import ResponsiveLayout from '@/components/layout/responsive-layout.vue'
import ResponsiveContainer from '@/components/layout/responsive-container.vue'
import ResponsiveGrid from '@/components/layout/responsive-grid.vue'
// {{ AURA-X: Add - 引入公共工具函数，减少重复代码. Approval: 寸止(ID:1735374000). }}
import utils from '@/common/js/utils.js'
export default {
  name: 'PlatformConfig',
  components: {
    ResponsiveLayout,
    ResponsiveContainer,
    ResponsiveGrid
  },
  data() {
    return {
      platformList: [],
      loading: false,
      currentTabIndex: 3 // 配置页面是第4个tab，索引为3（账号管理页面插入后）
    }
  },
  computed: {
    activeCount() {
      return this.platformList.filter(p => p.status === 1).length
    },
    loginCount() {
      return this.platformList.filter(p => p.login_status === 1).length
    }
  },
  onShow() {
    this.loadPlatformList()
  },
  methods: {
    async loadPlatformList() {
      this.loading = true
      try {
        const result = await callFunction('shelf-management', {
          action: 'getPlatformConfigs'
        })
        if (result.code === 0) {
          this.platformList = result.data.map(platform => ({
            ...platform,
            testing: false,
            syncing: false
          }))
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('加载平台配置失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    goToAdd() {
      uni.navigateTo({
        url: '/pages/platform-config/add'
      })
    },
    editPlatform(platform) {
      uni.navigateTo({
        url: `/pages/platform-config/add?id=${platform._id}&type=edit`
      })
    },
    async testLogin(platform) {
      platform.testing = true
      try {
        const result = await callFunction('shelf-management', {
          action: 'testPlatformLogin',
          data: {
            configId: platform._id, // {{ AURA-X: Add - 传递配置ID用于精确更新. Approval: 寸止(ID:1735373000). }}
            platformType: platform.platform_type
            // {{ AURA-X: Delete - 移除多余参数，云函数通过configId自动获取完整配置. Approval: 寸止(ID:1735373100). }}
          }
        })
        if (result.code === 0) {
          // {{ AURA-X: Modify - 使用公共成功提示函数. Approval: 寸止(ID:1735374000). }}
          utils.showSuccess('登录成功')
          this.loadPlatformList()
        } else {
          uni.showModal({
            title: '登录失败',
            content: result.message,
            showCancel: false
          })
        }
      } catch (error) {
        console.error('登录失败:', error)
        // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
        utils.showError('登录失败')
      } finally {
        platform.testing = false
      }
    },
    async syncPlatform(platform) {
      platform.syncing = true
      try {
        const result = await callFunction('shelf-management', {
          action: 'syncShelves',
          data: {
            platformType: platform.platform_type
          }
        })
        if (result.code === 0) {
          // {{ AURA-X: Modify - 使用公共成功提示函数. Approval: 寸止(ID:1735374000). }}
          utils.showSuccess(`更新成功，共${result.data.syncCount}个货架`)
          this.loadPlatformList()
        } else {
          uni.showModal({
            title: '更新失败',
            content: result.message,
            showCancel: false
          })
        }
      } catch (error) {
        console.error('更新货架失败:', error)
        // {{ AURA-X: Modify - 使用公共错误提示函数. Approval: 寸止(ID:1735374000). }}
        utils.showError('更新失败')
      } finally {
        platform.syncing = false
      }
    },
    deletePlatform(platform) {
      // {{ AURA-X: Modify - 精简删除操作，移除前端查询步骤. Approval: 寸止(ID:1735374200). }}
      uni.showModal({
        title: '⚠️ 确认删除',
        content: `删除平台配置"${platform.platform_name}"，此操作将：\n• 删除该平台配置\n• 删除该平台下所有关联货架\n\n确定要删除吗？`,
        confirmText: '确认删除',
        confirmColor: '#ff4757',
        success: async (res) => {
          if (res.confirm) {
            await this.executeDelete(platform)
          }
        }
      })
    },

    /**
     * 执行删除操作
     * @param {Object} platform 平台配置对象
     */
    async executeDelete(platform) {
      utils.showLoading('删除中...')

      try {
        const result = await callFunction('shelf-management', {
          action: 'deletePlatformConfig',
          data: {
            configId: platform._id
          }
        })

        if (result.code === 0) {
          const { deletedShelves, platformName } = result.data || {}
          const successMessage = deletedShelves > 0
            ? `删除成功！已删除平台"${platformName}"及其${deletedShelves}个关联货架`
            : `删除成功！已删除平台"${platformName}"`

          utils.showSuccess(successMessage)
          this.loadPlatformList()
        } else {
          utils.showError(result.message || '删除失败')
        }
      } catch (error) {
        console.error('删除平台配置失败:', error)
        utils.showError('删除失败，请重试')
      } finally {
        utils.hideLoading()
      }
    },
    getStatusClass(status) {
      switch (status) {
        case 1: return 'success'
        case 2: return 'error'
        default: return 'warning'
      }
    },
    getStatusText(status) {
      switch (status) {
        case 1: return '已登录'
        case 2: return '登录失效'
        default: return '未登录'
      }
    },
    formatTime(timestamp) {
      // {{ AURA-X: Modify - 使用公共时间格式化函数，保持特殊默认值. Approval: 寸止(ID:1735374000). }}
      if (!timestamp) return '从未登录'
      return utils.formatTime(timestamp)
    }
  }
}
</script> 
<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  @include responsive-spacing(padding-bottom, base);
  @include responsive-spacing(padding-top, base);
}

.header-title {
  @include responsive-font(xl);
  font-weight: $font-weight-bold;
  color: $text-color-primary;
}

.stats {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  @include responsive-spacing(padding, lg);
  box-shadow: $shadow-base;
  @include responsive-spacing(margin-bottom, lg);

  // 移动端优化
  @include mobile-only {
    padding: $spacing-base;
    margin-bottom: $spacing-base;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: $spacing-sm;

  @include desktop-up {
    gap: $spacing-base;
  }
}

.stat-item {
  text-align: center;
  @include responsive-spacing(padding, sm);
  border-radius: $border-radius-base;
  transition: all $transition-base;

  // 移动端优化
  @include mobile-only {
    padding: $spacing-xs $spacing-xs;
  }

  @include desktop-up {
    &:hover {
      background: $bg-color-hover;
    }
  }
}

.stat-value {
  @include responsive-font(xxxl);
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: 4rpx;
  display: block;

  // 移动端字体调整
  @include mobile-only {
    font-size: $font-size-xl;
    margin-bottom: 2rpx;
  }
}

.stat-label {
  @include responsive-font(sm);
  color: $text-color-secondary;
  font-weight: $font-weight-normal;

  // 移动端字体调整
  @include mobile-only {
    font-size: $font-size-xs;
  }
}
/* 平台列表 - 网格布局由responsive-grid组件处理 */

.platform-item {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  @include responsive-spacing(padding, xl);
  box-shadow: $shadow-base;
  transition: all $transition-base;

  // 移动端优化
  @include mobile-only {
    padding: $spacing-base;
    border-radius: $border-radius-base;
  }

  @include desktop-up {
    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
    }
  }
}
.platform-item:last-child {
  margin-bottom: $spacing-base;
}

.platform-info {
  @include responsive-spacing(margin-bottom, base);
}

.platform-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  @include responsive-spacing(margin-bottom, base);
}

.platform-name {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.status-tag {
  font-size: $font-size-xs;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;
  display: inline-block;

  &.success {
    background-color: $success-light;
    color: $success-color;
  }

  &.error {
    background-color: $error-light;
    color: $error-color;
  }

  &.warning {
    background-color: $warning-light;
    color: $warning-color;
  }
}

.platform-details {
  margin-bottom: $spacing-base;
}

.detail-item {
  display: flex;
  align-items: center;
  min-width: 0;
  flex: 1;
  margin-bottom: 12rpx;
}

.detail-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-right: 8rpx;
  white-space: nowrap;
  width: 160rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: $font-size-sm;
  color: $text-color-primary;
  word-break: break-all;
}

.platform-actions {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
}

.empty-state {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: $spacing-lg;
  color: $text-color-placeholder;
}

.empty-text {
  font-size: $font-size-lg;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
  font-weight: $font-weight-medium;
}

.empty-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  color: $text-color-secondary;
}
</style>