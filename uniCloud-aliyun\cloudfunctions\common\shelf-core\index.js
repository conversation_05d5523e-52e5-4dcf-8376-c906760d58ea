'use strict'

/**
 * 货架系统核心公共模块
 * 统一导出所有核心功能模块
 */

const StateManager = require('./lib/state-manager')
const PlatformAdapterFactory = require('./lib/platform-adapter-factory')
const Logger = require('./lib/logger')

// {{ AURA-X: Add - 新增优化模块. Approval: 寸止(ID:1735372900). }}
const AlertManager = require('./lib/alert-manager')
const DatabaseManager = require('./lib/database-manager')

// 导出适配器基类和具体实现
const BaseAdapter = require('./lib/adapters/base-adapter')
const ZuhaoWanAdapter = require('./lib/adapters/zuhaowan-adapter')
const UhaoZuAdapter = require('./lib/adapters/uhaozu-adapter')

module.exports = {
  // 核心管理器
  StateManager,
  PlatformAdapterFactory,
  Logger,
  AlertManager,
  DatabaseManager,

  // 适配器类
  BaseAdapter,
  ZuhaoWanAdapter,
  UhaoZuAdapter,

  // 便捷方法
  createAdapter: PlatformAdapterFactory.create.bind(PlatformAdapterFactory),
  getSupportedPlatforms: PlatformAdapterFactory.getSupportedPlatforms.bind(PlatformAdapterFactory),

  // 状态相关便捷方法
  convertZuhaoWanStatus: StateManager.convertZuhaoWanStatus.bind(StateManager),
  convertUhaoZuStatus: StateManager.convertUhaoZuStatus.bind(StateManager),
  getStateText: StateManager.getStateText.bind(StateManager),
  isValidState: StateManager.isValidState.bind(StateManager)
}
